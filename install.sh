#!/bin/bash
#
# zcli installer script for MinIO distribution
#
# This script downloads and installs zcli from a self-hosted MinIO server.
#
# IMPORTANT:
# 1. You MUST edit the MINIO_PUBLIC_URL variable below to your MinIO's public URL.
# 2. The MinIO bucket must allow public/anonymous read access.
#
# Usage:
# curl -sSL <URL_to_this_script> | bash
#

set -e

# --- Configuration ---
# !!! IMPORTANT !!!
# !!! REPLACE THIS with your MinIO's public-facing URL !!!
MINIO_PUBLIC_URL="https://api.minio.pikez.fun"

MINIO_BUCKET="homelab"
MINIO_PATH="zcli"
BINARY_NAME="zcli"
INSTALL_DIR="/usr/local/bin"

# --- Helper Functions ---
info() {
    echo "INFO: $1"
}

error() {
    echo "ERROR: $1" >&2
    exit 1
}

# --- Main Installation Logic ---
main() {
    # 0. Pre-flight check
    if [[ "$MINIO_PUBLIC_URL" == "http://YOUR_MINIO_SERVER:9000" ]]; then
        error "Please edit this script and set the MINIO_PUBLIC_URL variable."
    fi

    # 1. Detect OS and Architecture
    OS=""
    ARCH=""
    case "$(uname -s)" in
        Linux)
            OS="linux"
            ;;
        Darwin)
            OS="darwin"
            ;;
        *)
            error "Unsupported operating system: $(uname -s). Only Linux and macOS are supported."
            ;;
    esac

    case "$(uname -m)" in
        x86_64)
            ARCH="amd64"
            ;;
        arm64 | aarch64)
            ARCH="arm64"
            ;;
        *)
            error "Unsupported architecture: $(uname -m). Only amd64 is supported."
            ;;
    esac

    info "Detected OS: $OS, Arch: $ARCH"

    # 2. Construct the download URL
    FILENAME="${BINARY_NAME}-${OS}-${ARCH}"
    DOWNLOAD_URL="${MINIO_PUBLIC_URL}/${MINIO_BUCKET}/${MINIO_PATH}/${FILENAME}"

    info "Downloading from $DOWNLOAD_URL"

    # 3. Download the binary to a temporary directory
    TMP_DIR=$(mktemp -d)
    # Clean up the temp directory on script exit
    trap 'rm -rf "$TMP_DIR"' EXIT

    # Use --fail to ensure curl exits with an error if the download fails
    curl -sSL --fail -o "${TMP_DIR}/${BINARY_NAME}" "$DOWNLOAD_URL"

    # 4. Make the binary executable
    chmod +x "${TMP_DIR}/${BINARY_NAME}"

    # 5. Move the binary to the installation directory (requires sudo)
    info "Installing zcli to ${INSTALL_DIR}..."
    info "This step requires sudo permissions to move the file."

    if [ -w "$INSTALL_DIR" ]; then
        # If directory is writable without sudo
        mv "${TMP_DIR}/${BINARY_NAME}" "${INSTALL_DIR}/${BINARY_NAME}"
    else
        # Use sudo
        sudo mv "${TMP_DIR}/${BINARY_NAME}" "${INSTALL_DIR}/${BINARY_NAME}"
    fi

    # 6. Verify installation
    if ! command -v $BINARY_NAME &> /dev/null; then
        error "Installation failed. Please check your PATH or run the script again."
    fi

    info "zcli installed successfully!"
    info "Run 'zcli --help' to get started."

    # Show version
    $BINARY_NAME version
}

# --- Run ---
main
