package main

import (
	"log"

	"github.com/gin-gonic/gin"
	"github.com/pikez/zcli/api/models"
	"github.com/pikez/zcli/api/routes"
	"github.com/pikez/zcli/config"
	"github.com/pikez/zcli/pkg/database"
	"github.com/spf13/viper"

	// swagger embed files
	swaggerFiles "github.com/swaggo/files"
	// gin-swagger middleware
	ginSwagger "github.com/swaggo/gin-swagger"

	_ "github.com/pikez/zcli/api/docs" // docs is generated by Swag CLI, you have to import it.
)

// @title ZCLI API
// @version 1.0
// @description This is the API for ZCLI application.
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /
// @schemes http
func main() {
	// Load config
	viper.AddConfigPath(".")
	viper.SetConfigType("yaml")
	viper.SetConfigName(".zcli")

	if err := viper.ReadInConfig(); err != nil {
		log.Fatalf("Error reading config file: %v", err)
	}

	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Error loading config: %v", err)
	}

	// Initialize database connection
	if err := database.InitDB(cfg.MySQL); err != nil {
		log.Fatalf("Error initializing database: %v", err)
	}

	r := gin.Default()

	// AutoMigrate will create/update tables based on models
	if err := database.DB.AutoMigrate(&models.FundPosition{}); err != nil {
		log.Fatalf("Failed to auto migrate database: %v", err)
	}

	r.GET("/ping", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "pong",
		})
	})

	routes.RegisterFundRoutes(r)

	// Swagger UI
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	log.Println("Gin server starting on :8080")
	if err := r.Run(":8080"); err != nil {
		log.Fatalf("Gin server failed to start: %v", err)
	}
}
