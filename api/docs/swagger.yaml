basePath: /
definitions:
  models.BuyFundRequest:
    properties:
      buy_amount:
        type: number
      buy_date:
        description: YYYY-MM-DD format, defaults to today
        type: string
      buy_fee:
        description: defaults to 0
        type: number
      fund_code:
        type: string
    type: object
  models.BuyFundResponse:
    properties:
      buy_amount:
        type: number
      buy_date:
        type: string
      buy_fee:
        type: number
      fund_code:
        type: string
      message:
        type: string
      net_value:
        type: number
      record_id:
        type: integer
      shares:
        type: number
    type: object
  models.ErrorResponse:
    properties:
      error:
        type: string
    type: object
  models.FundPosition:
    properties:
      buy_amount:
        type: number
      buy_date:
        type: string
      buy_fee:
        type: number
      created_at:
        type: string
      deleted_at:
        description: Changed from gorm.DeletedAt
        type: string
      fund_code:
        type: string
      id:
        type: integer
      net_value:
        type: number
      shares:
        type: number
      updated_at:
        type: string
    type: object
  models.ListFundsResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/models.FundPosition'
        type: array
      message:
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is the API for ZCLI application.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: ZCLI API
  version: "1.0"
paths:
  /fund/buy:
    post:
      consumes:
      - application/json
      description: Buy a fund with specified amount and date
      parameters:
      - description: Buy fund request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.BuyFundRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Fund buy request processed successfully
          schema:
            $ref: '#/definitions/models.BuyFundResponse'
        "400":
          description: Invalid request payload
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Net value for the specified buy date not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Buy a fund
      tags:
      - funds
  /fund/list:
    get:
      description: Retrieve a list of all fund positions
      produces:
      - application/json
      responses:
        "200":
          description: Fund positions retrieved successfully
          schema:
            $ref: '#/definitions/models.ListFundsResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: List all fund positions
      tags:
      - funds
schemes:
- http
swagger: "2.0"
