{"schemes": ["http"], "swagger": "2.0", "info": {"description": "This is the API for ZCLI application.", "title": "ZCLI API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/", "paths": {"/fund/buy": {"post": {"description": "Buy a fund with specified amount and date", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["funds"], "summary": "Buy a fund", "parameters": [{"description": "Buy fund request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.BuyFundRequest"}}], "responses": {"200": {"description": "Fund buy request processed successfully", "schema": {"$ref": "#/definitions/models.BuyFundResponse"}}, "400": {"description": "Invalid request payload", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Net value for the specified buy date not found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/fund/list": {"get": {"description": "Retrieve a list of all fund positions", "produces": ["application/json"], "tags": ["funds"], "summary": "List all fund positions", "responses": {"200": {"description": "Fund positions retrieved successfully", "schema": {"$ref": "#/definitions/models.ListFundsResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}}, "definitions": {"models.BuyFundRequest": {"type": "object", "properties": {"buy_amount": {"type": "number"}, "buy_date": {"description": "YYYY-MM-DD format, defaults to today", "type": "string"}, "buy_fee": {"description": "defaults to 0", "type": "number"}, "fund_code": {"type": "string"}}}, "models.BuyFundResponse": {"type": "object", "properties": {"buy_amount": {"type": "number"}, "buy_date": {"type": "string"}, "buy_fee": {"type": "number"}, "fund_code": {"type": "string"}, "message": {"type": "string"}, "net_value": {"type": "number"}, "record_id": {"type": "integer"}, "shares": {"type": "number"}}}, "models.ErrorResponse": {"type": "object", "properties": {"error": {"type": "string"}}}, "models.FundPosition": {"type": "object", "properties": {"buy_amount": {"type": "number"}, "buy_date": {"type": "string"}, "buy_fee": {"type": "number"}, "created_at": {"type": "string"}, "deleted_at": {"description": "Changed from gorm.DeletedAt", "type": "string"}, "fund_code": {"type": "string"}, "id": {"type": "integer"}, "net_value": {"type": "number"}, "shares": {"type": "number"}, "updated_at": {"type": "string"}}}, "models.ListFundsResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.FundPosition"}}, "message": {"type": "string"}}}}}