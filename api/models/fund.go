package models

import (
	"time"
)

// FundPosition represents a fund buy record in the database
type FundPosition struct {
	ID        uint       `gorm:"primarykey" json:"id"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `gorm:"index" json:"deleted_at,omitempty"` // Changed from gorm.DeletedAt
	FundCode  string     `gorm:"type:varchar(255);not null" json:"fund_code"`
	BuyDate   time.Time  `gorm:"type:date;not null" json:"buy_date"`
	NetValue  float64    `gorm:"type:decimal(10,4);not null" json:"net_value"`
	BuyAmount float64    `gorm:"type:decimal(10,2);not null" json:"buy_amount"`
	BuyFee    float64    `gorm:"type:decimal(10,2);not null" json:"buy_fee"`
	Shares    float64    `gorm:"type:decimal(10,4);not null" json:"shares"`
}

// FundTrade represents a fund buy or sell record in the database
type FundTrade struct {
	ID          uint       `gorm:"primarykey" json:"id"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `gorm:"index" json:"deleted_at,omitempty"`
	FundCode    string     `gorm:"type:varchar(255);not null" json:"fund_code"`
	TradeDate   time.Time  `gorm:"type:date;not null" json:"trade_date"`
	TradeShares float64    `gorm:"type:decimal(10,4);not null" json:"trade_shares"` // Positive for buy, negative for sell
	TradeAmount float64    `gorm:"type:decimal(10,2);not null" json:"trade_amount"`
}
