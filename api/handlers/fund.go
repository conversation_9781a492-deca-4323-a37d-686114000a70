package handlers

import (
	"fmt"
	"log"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pikez/zcli/api/models"
	apimodels "github.com/pikez/zcli/pkg/api/models"
	"github.com/pikez/zcli/pkg/database"
	"github.com/pikez/zcli/pkg/fund"
)

// @Summary Buy a fund
// @Description Buy a fund with specified amount and date
// @Tags funds
// @Accept json
// @Produce json
// @Param request body apimodels.BuyFundRequest true "Buy fund request"
// @Success 200 {object} apimodels.BuyFundResponse "Fund buy request processed successfully"
// @Failure 400 {object} map[string]string "Invalid request payload"
// @Failure 404 {object} map[string]string "Net value for the specified buy date not found"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /fund/buy [post]
func BuyFund(c *gin.Context) {
	var req apimodels.BuyFundRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, models.ErrorResponse{Error: err.Error()})
		return
	}

	// Set default values
	if req.BuyDate == "" {
		req.BuyDate = time.Now().Format("2006-01-02")
	}

	// Parse the buy date
	buyDate, err := time.Parse("2006-01-02", req.BuyDate)
	if err != nil {
		c.JSON(400, models.ErrorResponse{Error: "Invalid buy_date format. Please use YYYY-MM-DD."})
		return
	}

	// Get fund info
	fundInfo, err := fund.GetFundInfoByEM(req.FundCode)
	if err != nil {
		c.JSON(500, models.ErrorResponse{Error: "Failed to get fund information: " + err.Error()})
		return
	}

	var netValue float64
	foundNetValue := false
	for _, data := range fundInfo.NetWorthTrend {
		if data.Date.Format("2006-01-02") == buyDate.Format("2006-01-02") {
			netValue = data.NetValue
			foundNetValue = true
			break
		}
	}

	if !foundNetValue {
		c.JSON(404, models.ErrorResponse{Error: "Net value for the specified buy date not found."})
		return
	}

	// Calculate shares
	shares := (req.BuyAmount - req.BuyFee) / netValue

	// Create fund buy record using GORM model
	positionRecord := models.FundPosition{
		FundCode:  req.FundCode,
		BuyDate:   buyDate,
		NetValue:  netValue,
		BuyAmount: req.BuyAmount,
		BuyFee:    req.BuyFee,
		Shares:    shares,
	}

	// Insert position record into database using GORM
	if result := database.DB.Create(&positionRecord); result.Error != nil {
		log.Printf("Error inserting fund buy position record: %v", result.Error)
		c.JSON(500, models.ErrorResponse{Error: "Failed to save fund buy position record."})
		return
	}

	// Create fund trade record
	tradeRecord := models.FundTrade{
		FundCode:    req.FundCode,
		TradeDate:   buyDate,
		TradeShares: shares,
		TradeAmount: req.BuyAmount,
	}

	// Insert trade record into database
	if result := database.DB.Create(&tradeRecord); result.Error != nil {
		log.Printf("Error inserting fund buy trade record: %v", result.Error)
		// This is a non-critical error for the main operation, but should be logged
	}

	log.Printf("Received fund buy request: %+v", req)
	log.Printf("Fund Code: %s, Buy Date: %s, Net Value: %.4f, Buy Amount: %.2f, Buy Fee: %.2f, Shares: %.4f",
		req.FundCode, req.BuyDate, netValue, req.BuyAmount, req.BuyFee, shares)

	c.JSON(200, apimodels.BuyFundResponse{
		Message:   "Fund buy request processed successfully",
		FundCode:  req.FundCode,
		BuyDate:   req.BuyDate,
		NetValue:  netValue,
		BuyAmount: req.BuyAmount,
		BuyFee:    req.BuyFee,
		Shares:    shares,
		RecordID:  positionRecord.ID,
	})
}})
		return
	}

	// Get fund info
	fundInfo, err := fund.GetFundInfoByEM(req.FundCode)
	if err != nil {
		c.JSON(500, models.ErrorResponse{Error: "Failed to get fund information: " + err.Error()})
		return
	}

	var netValue float64
	foundNetValue := false
	for _, data := range fundInfo.NetWorthTrend {
		if data.Date.Format("2006-01-02") == sellDate.Format("2006-01-02") {
			netValue = data.NetValue
			foundNetValue = true
			break
		}
	}

	if !foundNetValue {
		c.JSON(404, models.ErrorResponse{Error: "Net value for the specified sell date not found."})
		return
	}

	// Calculate shares (negative for sell)
	tradeShares := -(req.SellAmount / netValue)

	// Create fund trade record using GORM model
	record := models.FundTrade{
		FundCode:    req.FundCode,
		TradeDate:   sellDate,
		TradeShares: tradeShares,
		TradeAmount: req.SellAmount,
	}

	// Insert record into database using GORM
	if result := database.DB.Create(&record); result.Error != nil {
		log.Printf("Error inserting fund sell trade record: %v", result.Error)
		c.JSON(500, models.ErrorResponse{Error: "Failed to save fund sell trade record."})
		return
	}

	log.Printf("Received fund sell request: %+v", req)
	log.Printf("Fund Code: %s, Sell Date: %s, Net Value: %.4f, Sell Amount: %.2f, Shares: %.4f",
		req.FundCode, req.SellDate, netValue, req.SellAmount, tradeShares)

	c.JSON(200, apimodels.SellFundResponse{
		Message:     "Fund sell request processed successfully",
		FundCode:    req.FundCode,
		TradeDate:   req.SellDate,
		TradeShares: tradeShares,
		TradeAmount: req.SellAmount,
		RecordID:    record.ID,
	})
}
