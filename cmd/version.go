package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
)

func init() {
	rootCmd.AddCommand(versionCmd)
}

var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Print the version number, commit hash, build date, and branch/tag",
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Printf("Version:    %s\n", appVersion)
		fmt.Printf("Commit:     %s\n", appCommit)
		fmt.Printf("BuildDate:  %s\n", appBuildDate)
		fmt.Printf("Branch/Tag: %s\n", appBranchTag)
	},
}
