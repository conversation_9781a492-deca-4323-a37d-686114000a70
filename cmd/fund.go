package cmd

import (
	"bufio"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/olekukonko/tablewriter"
	"github.com/pikez/zcli/pkg/api"
	"github.com/pikez/zcli/pkg/api/models"
	"github.com/pikez/zcli/pkg/fund"
	"github.com/spf13/cobra"
)

// fundCmd represents the fund command
var fundCmd = &cobra.Command{
	Use:   "fund",
	Short: "管理您的基金持仓",
	Long:  `提供管理基金买卖记录和查看当前持仓的命令。`,
}

// buyCmd represents the open command
var buyCmd = &cobra.Command{
	Use:   "buy",
	Short: "添加新的基金买入记录",
	Long:  `通过交互式输入基金代码、买入日期、买入金额和买入费用来添加新的基金买入记录。`,
	Run: func(cmd *cobra.Command, args []string) {
		reader := bufio.NewReader(os.Stdin)

		fmt.Print("请输入基金代码 (例如, 000001): ")
		fundCode, _ := reader.ReadString('\n')
		fundCode = strings.TrimSpace(fundCode)

		defaultBuyDate := time.Now().Format("2006-01-02")
		fmt.Printf("请输入买入日期 (YYYY-MM-DD, 默认: %s): ", defaultBuyDate)
		buyDate, _ := reader.ReadString('\n')
		buyDate = strings.TrimSpace(buyDate)
		if buyDate == "" {
			buyDate = defaultBuyDate
		}

		fmt.Print("请输入买入金额 (例如, 1000.00): ")
		buyAmountStr, _ := reader.ReadString('\n')
		buyAmount, err := strconv.ParseFloat(strings.TrimSpace(buyAmountStr), 64)
		if err != nil {
			log.Fatalf("买入金额无效: %v", err)
		}

		fmt.Print("请输入买入费用 (默认: 0): ")
		buyFeeStr, _ := reader.ReadString('\n')
		buyFeeStr = strings.TrimSpace(buyFeeStr)
		buyFee := 0.0
		if buyFeeStr != "" {
			buyFee, err = strconv.ParseFloat(buyFeeStr, 64)
			if err != nil {
				log.Fatalf("买入费用无效: %v", err)
			}
		}

		client := api.NewClient(cfg.APIBaseURL, 5*time.Second)
		buyReq := models.BuyFundRequest{
			FundCode:  fundCode,
			BuyDate:   buyDate,
			BuyAmount: buyAmount,
			BuyFee:    buyFee,
		}

		resp, err := client.BuyFund(buyReq)
		if err != nil {
			log.Fatalf("买入基金失败: %v", err)
		}
		jsonResp, _ := json.MarshalIndent(resp, "", "  ")
		fmt.Println("响应:", string(jsonResp))
	},
}

// showCmd represents the show command
var showCmd = &cobra.Command{
	Use:   "show",
	Short: "显示当前基金持仓",
	Long:  `显示所有当前基金持仓列表，包括代码、名称、数量、成本、盈亏和收益率。`,
	Run: func(cmd *cobra.Command, args []string) {
		client := api.NewClient(cfg.APIBaseURL, 5*time.Second)
		resp, err := client.ListFunds()
		if err != nil {
			log.Fatalf("获取基金列表失败: %v", err)
		}

		data := resp.Data

		table := tablewriter.NewWriter(os.Stdout)
		table.Header([]string{"基金代码", "买入日期", "买入净值", "买入金额", "买入费用", "份额", "最新净值", "当前市值", "盈亏", "收益率"})

		for _, item := range data {
			fundInfo, err := fund.GetFundInfoByEM(item.FundCode)
			latestNetValue := 0.0
			if err != nil {
				log.Printf("无法获取基金 %s 的最新信息: %v", item.FundCode, err)
			} else {
				if len(fundInfo.NetWorthTrend) > 0 {
					latestNetValue = fundInfo.NetWorthTrend[len(fundInfo.NetWorthTrend)-1].NetValue
				}
			}

			currentValue := latestNetValue * item.Shares
			totalCost := item.BuyAmount + item.BuyFee
			profit := currentValue - totalCost
			yield := 0.0
			if totalCost != 0 {
				yield = (profit / totalCost) * 100
			}

			table.Append([]string{
				item.FundCode,
				item.BuyDate,
				fmt.Sprintf("%.2f", item.NetValue),
				fmt.Sprintf("%.2f", item.BuyAmount),
				fmt.Sprintf("%.2f", item.BuyFee),
				fmt.Sprintf("%.2f", item.Shares),
				fmt.Sprintf("%.2f", latestNetValue),
				fmt.Sprintf("%.2f", currentValue),
				fmt.Sprintf("%.2f", profit),
				fmt.Sprintf("%.2f%%", yield),
			})
		}
		table.Render()
	},
}

func init() {
	rootCmd.AddCommand(fundCmd)

	fundCmd.AddCommand(buyCmd)
	fundCmd.AddCommand(showCmd)
	fundCmd.AddCommand(tradesCmd)
}

// tradesCmd represents the trades command
var tradesCmd = &cobra.Command{
	Use:   "trades",
	Short: "显示基金交易记录",
	Long:  `显示所有基金的买入和卖出交易记录。`,
	Run: func(cmd *cobra.Command, args []string) {
		client := api.NewClient(cfg.APIBaseURL, 5*time.Second)
		resp, err := client.ListFundTrades()
		if err != nil {
			log.Fatalf("获取基金交易列表失败: %v", err)
		}

		data := resp.Data

		table := tablewriter.NewWriter(os.Stdout)
		table.Header([]string{"ID", "基金代码", "交易日期", "交易份额", "交易金额"})

		for _, item := range data {
			table.Append([]string{
				fmt.Sprintf("%d", item.ID),
				item.FundCode,
				item.TradeDate.Format("2006-01-02"),
				fmt.Sprintf("%.4f", item.TradeShares),
				fmt.Sprintf("%.2f", item.TradeAmount),
			})
		}
		table.Render()
	},
}
