package cmd

import (
	"fmt"
	"log"
	"net"
	"os"
	"os/exec"
	"strings"

	"github.com/pikez/zcli/pkg/headscale"
	"github.com/spf13/cobra"
)

var sshCmd = &cobra.Command{
	Use:   "ssh [node-name]",
	Short: "通过节点名称 SSH 登录到 Headscale 节点",
	Long:  "ssh 命令允许您通过提供 Headscale 节点的名称直接 SSH 登录到该节点。",
	Args:  cobra.ExactArgs(1), // 确保只提供一个参数：节点名称
	Run: func(cmd *cobra.Command, args []string) {
		nodeName := args[0]

		client := headscale.NewClient(cfg.HeadscaleURL, cfg.HeadscaleToken)

		resp, err := client.ListNodes()
		if err != nil {
			log.Fatalf("无法列出节点: %v", err)
		}

		var targetIP string
		found := false
		for _, node := range resp.Nodes {
			if strings.EqualFold(node.GivenName, nodeName) {
				found = true
				// 找到第一个 IPv4 地址
				for _, ipStr := range node.IPAddresses {
					ip := net.ParseIP(ipStr)
					if ip != nil && ip.To4() != nil {
						targetIP = ipStr
						break // 找到一个就退出
					}
				}
				break // 找到节点就退出
			}
		}

		if !found {
			log.Fatalf("未找到节点 '%s'", nodeName)
		}

		if targetIP == "" {
			log.Fatalf("节点 '%s' 没有可用的 IPv4 地址", nodeName)
		}

		fmt.Printf("正在 SSH 连接到 %s (%s)...", nodeName, targetIP)

		sshCmd := exec.Command("ssh", fmt.Sprintf("root@%s", targetIP))
		sshCmd.Stdin = os.Stdin
		sshCmd.Stdout = os.Stdout
		sshCmd.Stderr = os.Stderr

		err = sshCmd.Run()
		if err != nil {
			log.Fatalf("SSH 命令执行失败: %v", err)
		}
	},
}

func init() {
	rootCmd.AddCommand(sshCmd)
}
