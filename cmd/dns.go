package cmd

import (
	"fmt"
	"log"
	"net"
	"os"
	"regexp"

	"github.com/olekukonko/tablewriter"
	"github.com/pikez/zcli/pkg/cloudflare"
	"github.com/spf13/cobra"
)

const defaultDomain = "pikez.fun"

var dnsCmd = &cobra.Command{
	Use:   "dns",
	Short: "管理 DNS 记录",
	Long:  `dns 命令用于管理 DNS 记录，例如列出、添加、删除记录等。`,
}

var dnsListCmd = &cobra.Command{
	Use:   "list",
	Short: "列出 Cloudflare 域名的 DNS 记录",
	Long:  `list 命令用于列出指定 Cloudflare 域名的所有 DNS 记录。`,
	Run: func(cmd *cobra.Command, args []string) {
		client := cloudflare.NewClient(cfg.CloudflareKey)

		// 1. 获取 Zone ID
		zoneID, err := client.GetZoneID(defaultDomain)
		if err != nil {
			log.Fatalf("无法获取域名 %s 的 Zone ID: %v", defaultDomain, err)
		}
		if zoneID == "" {
			log.Fatalf("未找到域名 %s 对应的 Zone", defaultDomain)
		}

		// 2. 获取 DNS 记录
		dnsRecords, err := client.GetDNSRecords(zoneID)
		if err != nil {
			log.Fatalf("无法获取 DNS 记录: %v", err)
		}

		// 3. 打印输出
		table := tablewriter.NewWriter(os.Stdout)
		table.Header([]string{"类型", "名称", "内容", "代理", "TTL"})

		for _, record := range dnsRecords {
			proxyStatus := "否"
			if record.Proxied {
				proxyStatus = "是"
			}
			if record.Type == "TXT" {
				continue
			}
			table.Append([]string{
				record.Type,
				record.Name,
				record.Content,
				proxyStatus,
				fmt.Sprintf("%d", record.TTL),
			})
		}
		table.Render()
	},
}

var dnsCreateCmd = &cobra.Command{
	Use:   "create",
	Short: "创建 Cloudflare DNS 记录",
	Long:  `create 命令用于在 Cloudflare 中创建新的 DNS 记录。`,
	Run: func(cmd *cobra.Command, args []string) {
		recordType, _ := cmd.Flags().GetString("type")
		name, _ := cmd.Flags().GetString("name")
		content, _ := cmd.Flags().GetString("content")

		// 验证输入
		if recordType == "" || name == "" || content == "" {
			log.Fatal("类型、名称和内容都不能为空")
		}

		switch recordType {
		case "A":
			if net.ParseIP(content) == nil {
				log.Fatalf("对于 A 记录，内容必须是有效的 IP 地址: %s", content)
			}
		case "CNAME":
			// 简单的域名验证，可以根据需要进行更严格的验证
			// 域名不能包含空格，且至少包含一个点
			// 允许以数字开头，但不能以连字符开头或结尾
			// 允许包含字母、数字、连字符
			// 顶级域名至少两个字符
			// 完整的正则表达式可能更复杂，这里使用一个简化版本
			// 确保不是IP地址
			if net.ParseIP(content) != nil || !regexp.MustCompile(`^[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$`).MatchString(content) {
				log.Fatalf("对于 CNAME 记录，内容必须是有效的域名: %s", content)
			}
		default:
			log.Fatalf("不支持的 DNS 记录类型: %s", recordType)
		}

		client := cloudflare.NewClient(cfg.CloudflareKey)

		zoneID, err := client.GetZoneID(defaultDomain)
		if err != nil {
			log.Fatalf("无法获取域名 %s 的 Zone ID: %v", defaultDomain, err)
		}
		if zoneID == "" {
			log.Fatalf("未找到域名 %s 对应的 Zone", defaultDomain)
		}

		err = client.CreateDNSRecord(zoneID, recordType, name, content)
		if err != nil {
			log.Fatalf("创建 DNS 记录失败: %v", err)
		}

		fmt.Printf("成功创建 DNS 记录: 类型=%s, 名称=%s, 内容=%s\n", recordType, name, content)
	},
}

var dnsDeleteCmd = &cobra.Command{
	Use:   "delete",
	Short: "删除 Cloudflare DNS 记录",
	Long:  `delete 命令用于删除 Cloudflare 中的 DNS 记录。`,
	Run: func(cmd *cobra.Command, args []string) {
		name, _ := cmd.Flags().GetString("name")

		if name == "" {
			log.Fatal("名称不能为空")
		}

		client := cloudflare.NewClient(cfg.CloudflareKey)

		zoneID, err := client.GetZoneID(defaultDomain)
		if err != nil {
			log.Fatalf("无法获取域名 %s 的 Zone ID: %v", defaultDomain, err)
		}
		if zoneID == "" {
			log.Fatalf("未找到域名 %s 对应的 Zone", defaultDomain)
		}

		dnsRecords, err := client.GetDNSRecords(zoneID)
		if err != nil {
			log.Fatalf("无法获取 DNS 记录: %v", err)
		}

		foundRecords := []cloudflare.DNSRecord{}
		for _, record := range dnsRecords {
			if record.Name == name {
				foundRecords = append(foundRecords, record)
			}
		}

		if len(foundRecords) == 0 {
			log.Fatalf("未找到名称为 %s 的 DNS 记录", name)
		}

		for _, record := range foundRecords {
			err = client.DeleteDNSRecord(zoneID, record.ID)
			if err != nil {
				log.Fatalf("删除 DNS 记录 %s (ID: %s) 失败: %v", record.Name, record.ID, err)
			}
			fmt.Printf("成功删除 DNS 记录: 类型=%s, 名称=%s, 内容=%s\n", record.Type, record.Name, record.Content)
		}
	},
}

func init() {
	rootCmd.AddCommand(dnsCmd)
	dnsCmd.AddCommand(dnsListCmd)

	dnsCmd.AddCommand(dnsCreateCmd)
	dnsCreateCmd.Flags().StringP("type", "t", "", "DNS 记录类型 (例如: A, CNAME)")
	dnsCreateCmd.Flags().StringP("name", "n", "", "DNS 记录名称 (例如: example.com)")
	dnsCreateCmd.Flags().StringP("content", "c", "", "DNS 记录内容 (IP 地址或目标域名)")
	dnsCreateCmd.MarkFlagRequired("type")
	dnsCreateCmd.MarkFlagRequired("name")
	dnsCreateCmd.MarkFlagRequired("content")

	dnsCmd.AddCommand(dnsDeleteCmd)
	dnsDeleteCmd.Flags().StringP("name", "n", "", "要删除的 DNS 记录名称 (例如: example.com)")
	dnsDeleteCmd.MarkFlagRequired("name")
}
