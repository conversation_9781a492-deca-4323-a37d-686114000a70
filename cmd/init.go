package cmd

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"

	"github.com/spf13/cobra"
)

var initCmd = &cobra.Command{
	Use:   "init",
	Short: "初始化 zcli 配置",
	Long:  `init 命令用于初始化 zcli 的配置文件和相关设置。`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println("正在下载 .zcli.yaml 配置...")

		// 目标 URL
		url := "https://api.minio.pikez.fun/homelab/zcli/.zcli.yaml"

		// 发送 HTTP GET 请求
		resp, err := http.Get(url)
		if err != nil {
			log.Fatalf("无法下载配置文件: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			log.Fatalf("下载配置文件失败，状态码: %d", resp.StatusCode)
		}

		// 获取用户主目录
		homeDir, err := os.UserHomeDir()
		if err != nil {
			log.Fatalf("无法获取用户主目录: %v", err)
		}

		// 构建目标文件路径
		filePath := filepath.Join(homeDir, ".zcli.yaml")

		// 创建文件
		out, err := os.Create(filePath)
		if err != nil {
			log.Fatalf("无法创建配置文件 %s: %v", filePath, err)
		}
		defer out.Close()

		// 将下载的内容写入文件
		_, err = io.Copy(out, resp.Body)
		if err != nil {
			log.Fatalf("无法写入配置文件 %s: %v", filePath, err)
		}

		fmt.Printf("配置文件已成功下载到 %s\n", filePath)
	},
}

func init() {
	rootCmd.AddCommand(initCmd)
}
