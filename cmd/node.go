package cmd

import (
	"fmt"
	"log"
	"net"
	"os"
	"strings"

	"github.com/olekukonko/tablewriter"
	"github.com/pikez/zcli/pkg/headscale"
	"github.com/spf13/cobra"
)

var nodeCmd = &cobra.Command{
	Use:   "node",
	Short: "管理 Headscale 节点",
	Long:  `node 命令用于管理 Headscale 服务器上的节点，例如列出、删除、移动节点等。`,
}

var listCmd = &cobra.Command{
	Use:   "list",
	Short: "列出 Headscale 中的所有节点",
	Long:  `列出 Headscale 服务器上注册的所有节点。`,
	Run: func(cmd *cobra.Command, args []string) {
		client := headscale.NewClient(cfg.HeadscaleURL, cfg.HeadscaleToken)

		resp, err := client.ListNodes()
		if err != nil {
			log.Fatalf("无法列出节点: %v", err)
		}

		table := tablewriter.NewWriter(os.Stdout)
		table.Header([]string{"ID", "主机名", "IP 地址", "用户", "在线"})

		for _, node := range resp.Nodes {
			ipv4Addresses := []string{}
			for _, ipStr := range node.IPAddresses {
				ip := net.ParseIP(ipStr)
				if ip != nil && ip.To4() != nil {
					ipv4Addresses = append(ipv4Addresses, ipStr)
				}
			}
			table.Append([]string{
				fmt.Sprintf("%d", node.ID),
				node.GivenName,
				strings.Join(ipv4Addresses, ", "),
				node.User.Name,
				fmt.Sprintf("%t", node.Online),
			})
		}
		table.Render()
	},
}

func init() {
	rootCmd.AddCommand(nodeCmd)
	nodeCmd.AddCommand(listCmd)
}
