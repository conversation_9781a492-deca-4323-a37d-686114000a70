package api

import (
	"fmt"
	"time"

	"github.com/pikez/zcli/pkg/api/models"
	"github.com/pikez/zcli/pkg/httpClient"
)

// Client represents the API client for ZCLI.
type Client struct {
	HTTPClient *httpClient.Client
}

// NewClient creates a new API client.
func NewClient(baseURL string, timeout time.Duration) *Client {
	return &Client{
		HTTPClient: httpClient.NewClient(baseURL, timeout),
	}
}

// BuyFund sends a request to buy a fund.
func (c *Client) BuyFund(req models.BuyFundRequest) (models.BuyFundResponse, error) {
	var result models.BuyFundResponse
	err := c.HTTPClient.Do("POST", "/fund/buy", req, &result)
	if err != nil {
		return models.BuyFundResponse{}, fmt.Errorf("failed to buy fund: %w", err)
	}
	return result, nil
}

// SellFund sends a request to sell a fund.
func (c *Client) SellFund(req models.SellFundRequest) (models.SellFundResponse, error) {
	var result models.SellFundResponse
	err := c.HTTPClient.Do("POST", "/fund/sell", req, &result)
	if err != nil {
		return models.SellFundResponse{}, fmt.Errorf("failed to sell fund: %w", err)
	}
	return result, nil
}

// ListFunds retrieves a list of all fund positions.
func (c *Client) ListFunds() (models.ListFundsResponse, error) {
	var result models.ListFundsResponse
	err := c.HTTPClient.Do("GET", "/fund/list", nil, &result)
	if err != nil {
		return models.ListFundsResponse{}, fmt.Errorf("failed to list funds: %w", err)
	}
	return result, nil
}

// ListFundTrades retrieves a list of all fund trades.
func (c *Client) ListFundTrades() (models.ListFundTradesResponse, error) {
	var result models.ListFundTradesResponse
	err := c.HTTPClient.Do("GET", "/fund/trades", nil, &result)
	if err != nil {
		return models.ListFundTradesResponse{}, fmt.Errorf("failed to list fund trades: %w", err)
	}
	return result, nil
}