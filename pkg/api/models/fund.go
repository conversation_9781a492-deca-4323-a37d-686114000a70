package models

import (
	"time"
)

// FundPosition represents a fund position (open buy record)
type FundPosition struct {
	FundCode  string  `json:"fund_code"`
	BuyDate   string  `json:"buy_date"`
	NetValue  float64 `json:"net_value"`
	BuyAmount float64 `json:"buy_amount"`
	BuyFee    float64 `json:"buy_fee"`
	Shares    float64 `json:"shares"`
}

// BuyFundRequest defines the request body for buying a fund
type BuyFundRequest struct {
	FundCode  string  `json:"fund_code" binding:"required"`
	BuyDate   string  `json:"buy_date"` // YYYY-MM-DD format, defaults to today
	BuyAmount float64 `json:"buy_amount" binding:"required"`
	BuyFee    float64 `json:"buy_fee"` // defaults to 0
}

// BuyFundResponse defines the response body for buying a fund
type BuyFundResponse struct {
	FundPosition
	Message string `json:"message"`
	RecordID uint `json:"record_id"`
}

// ListFundsResponse defines the response body for listing all fund positions
type ListFundsResponse struct {
	Message string         `json:"message"`
	Data    []FundPosition `json:"data"`
}

// SellFundRequest defines the request body for selling a fund
type SellFundRequest struct {
	FundCode string  `json:"fund_code" binding:"required"`
	SellDate string  `json:"sell_date"` // YYYY-MM-DD format, defaults to today
	Shares   float64 `json:"shares" binding:"required"`
}

// SellFundResponse defines the response body for selling a fund
type SellFundResponse struct {
	FundPosition
	Message      string  `json:"message"`
	SellNetValue float64 `json:"sell_net_value"`
	SellAmount   float64 `json:"sell_amount"`
	Profit       float64 `json:"profit"`
	Yield        float64 `json:"yield"`
	RecordID     uint    `json:"record_id"`
}

// ListFundTradesResponse defines the response body for listing all fund trades
type ListFundTradesResponse struct {
	Message string      `json:"message"`
	Data    []FundTrade `json:"data"`
}

// FundTrade represents a fund buy or sell record in the database
type FundTrade struct {
	ID          uint      `json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	FundCode    string    `json:"fund_code"`
	TradeDate   time.Time `json:"trade_date"`
	TradeShares float64   `json:"trade_shares"`
	TradeAmount float64   `json:"trade_amount"`
}