package cloudflare

import (
	"fmt"
	"net/http"
	"time"

	"github.com/pikez/zcli/pkg/httpClient"
)

const cloudflareAPI = "https://api.cloudflare.com/client/v4"

// Client represents the Cloudflare API client.
type Client struct {
	HTTPClient *httpClient.Client
}

// NewClient creates a new Cloudflare API client.
func NewClient(apiKey string) *Client {
	hc := httpClient.NewClient(cloudflareAPI, 10*time.Second)
	hc.SetHeader("Authorization", "Bearer "+apiKey)
	hc.SetHeader("Content-Type", "application/json")
	return &Client{
		HTTPClient: hc,
	}
}

// GetZoneID fetches the Zone ID for a given domain.
func (c *Client) GetZoneID(domain string) (string, error) {
	path := fmt.Sprintf("/zones?name=%s", domain)
	var zoneResp ZoneListResponse
	if err := c.HTTPClient.Do(http.MethodGet, path, nil, &zoneResp); err != nil {
		return "", fmt.Errorf("获取 Zone ID 失败: %w", err)
	}

	if !zoneResp.Success {
		return "", fmt.Errorf("获取 Zone ID 失败: %v", zoneResp.Errors)
	}

	if len(zoneResp.Result) == 0 {
		return "", nil // No zone found
	}

	return zoneResp.Result[0].ID, nil
}

// GetDNSRecords fetches DNS records for a given Zone ID.
func (c *Client) GetDNSRecords(zoneID string) ([]DNSRecord, error) {
	path := fmt.Sprintf("/zones/%s/dns_records", zoneID)
	var dnsResp DNSRecordListResponse
	if err := c.HTTPClient.Do(http.MethodGet, path, nil, &dnsResp); err != nil {
		return nil, fmt.Errorf("获取 DNS 记录失败: %w", err)
	}

	if !dnsResp.Success {
		return nil, fmt.Errorf("获取 DNS 记录失败: %v", dnsResp.Errors)
	}

	return dnsResp.Result, nil
}

// CreateDNSRecord creates a new DNS record for a given Zone ID.
func (c *Client) CreateDNSRecord(zoneID, recordType, name, content string) error {
	path := fmt.Sprintf("/zones/%s/dns_records", zoneID)

	record := CreateDNSRecordRequest{
		Type:    recordType,
		Name:    name,
		Content: content,
		TTL:     1,    // Automatic TTL
		Proxied: false, // Not proxied by default
	}

	var dnsResp DNSRecordResponse
	if err := c.HTTPClient.Do(http.MethodPost, path, record, &dnsResp); err != nil {
		return fmt.Errorf("创建 DNS 记录失败: %w", err)
	}

	if !dnsResp.Success {
		return fmt.Errorf("创建 DNS 记录失败: %v", dnsResp.Errors)
	}

	return nil
}

// DeleteDNSRecord deletes a DNS record for a given Zone ID and Record ID.
func (c *Client) DeleteDNSRecord(zoneID, recordID string) error {
	path := fmt.Sprintf("/zones/%s/dns_records/%s", zoneID, recordID)

	var dnsResp DNSRecordResponse
	if err := c.HTTPClient.Do(http.MethodDelete, path, nil, &dnsResp); err != nil {
		return fmt.Errorf("删除 DNS 记录失败: %w", err)
	}

	if !dnsResp.Success {
		return fmt.Errorf("删除 DNS 记录失败: %v", dnsResp.Errors)
	}

	return nil
}
