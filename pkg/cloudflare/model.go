package cloudflare

// Cloudflare API common response structure
type CloudflareResponse struct {
	Success  bool        `json:"success"`
	Errors   []CFError   `json:"errors"`
	Messages []CFMessage `json:"messages"`
}

type CFError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type CFMessage struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// Zone List Response
type ZoneListResponse struct {
	CloudflareResponse
	Result []Zone `json:"result"`
}

type Zone struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// DNS Record List Response
type DNSRecordListResponse struct {
	CloudflareResponse
	Result []DNSRecord `json:"result"`
}

// DNS Record Response for single record
type DNSRecordResponse struct {
	CloudflareResponse
	Result DNSRecord `json:"result"`
}

// CreateDNSRecordRequest represents the request body for creating a DNS record.
type CreateDNSRecordRequest struct {
	Type    string `json:"type"`
	Name    string `json:"name"`
	Content string `json:"content"`
	TTL     int    `json:"ttl"`
	Proxied bool   `json:"proxied"`
}

type DNSRecord struct {
	ID         string `json:"id"`
	Type       string `json:"type"`
	Name       string `json:"name"`
	Content    string `json:"content"`
	Proxied    bool   `json:"proxied"`
	TTL        int    `json:"ttl"`
	ZoneID     string `json:"zone_id"`
	ZoneName   string `json:"zone_name"`
	CreatedOn  string `json:"created_on"`
	ModifiedOn string `json:"modified_on"`
}