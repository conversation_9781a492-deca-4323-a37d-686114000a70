package fund

import (
	"testing"
)

func TestGetFundInfoByEM(t *testing.T) {
	fundCode := "159568" // Example fund code for testing
	fundInfo, err := GetFundInfoByEM(fundCode)
	if err != nil {
		t.Fatalf("GetFundInfoByEM(%s) failed: %v", fundCode, err)
	}

	if fundInfo == nil {
		t.Fatalf("GetFundInfoByEM(%s) returned nil FundInfo", fundCode)
	}

	if fundInfo.FSCode != fundCode {
		t.Errorf("Expected fund code %s, got %s", fundCode, fundInfo.FSCode)
	}

	if len(fundInfo.NetWorthTrend) == 0 {
		t.<PERSON><PERSON><PERSON>("NetWorthTrend is empty")
	}
	if len(fundInfo.ACWorthTrend) == 0 {
		t.<PERSON><PERSON>rf("ACWorthTrend is empty")
	}
	if len(fundInfo.GrandTotal) == 0 {
		t.<PERSON>("GrandTotal is empty")
	}

	// Verify GrandTotal data parsing
	if len(fundInfo.GrandTotal) > 0 {
		// Check a few data points to ensure all three values are parsed
		// This is a basic check, more robust checks would involve comparing with known values
		for _, data := range fundInfo.GrandTotal {
			if data.FundReturn == 0 && data.SimilarAvgReturn == 0 && data.CSI300Return == 0 {
				t.Logf("Warning: GrandTotal data point for %s has all zero returns. This might be expected for some dates.", data.Date.Format("2006-01-02"))
			}
		}
	}

	t.Logf("Successfully fetched and parsed fund info for %s: %s", fundCode, fundInfo.FSName)
	t.Logf("NetWorthTrend count: %d", len(fundInfo.NetWorthTrend))
	t.Logf("ACWorthTrend count: %d", len(fundInfo.ACWorthTrend))
	t.Logf("GrandTotal count: %d", len(fundInfo.GrandTotal))

	// Optional: Print some parsed data for visual inspection during development
	if len(fundInfo.GrandTotal) > 0 {
		t.Logf("First GrandTotal entry: %+v", fundInfo.GrandTotal[0])
		t.Logf("Last GrandTotal entry: %+v", fundInfo.GrandTotal[len(fundInfo.GrandTotal)-1])
	}
}
