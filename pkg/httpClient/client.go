package httpClient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// Client represents a generic HTTP client.
type Client struct {
	BaseURL    string
	HTTPClient *http.Client
	// Headers to be sent with every request
	Headers map[string]string
}

// NewClient creates a new generic HTTP client.
func NewClient(baseURL string, timeout time.Duration) *Client {
	return &Client{
		BaseURL:    baseURL,
		HTTPClient: &http.Client{Timeout: timeout},
		Headers:    make(map[string]string),
	}
}

// SetHeader sets a header that will be sent with every request.
func (c *Client) SetHeader(key, value string) {
	c.Head<PERSON>[key] = value
}

// Do sends an HTTP request and unmarshals the response body into the given interface.
// If the request body is not nil, it will be marshaled to JSON.
func (c *Client) Do(method, path string, requestBody interface{}, responseBody interface{}) error {
	var reqBody io.Reader
	if requestBody != nil {
		jsonBody, err := json.Marshal(requestBody)
		if err != nil {
			return fmt.Errorf("failed to marshal request body: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonBody)
	}

	url := c.BaseURL + path
	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	for key, value := range c.Headers {
		req.Header.Set(key, value)
	}
	if requestBody != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode < http.StatusOK || resp.StatusCode >= http.StatusBadRequest {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(bodyBytes))
	}

	if responseBody != nil {
		if err := json.NewDecoder(resp.Body).Decode(responseBody); err != nil {
			return fmt.Errorf("failed to decode response: %w", err)
		}
	}

	return nil
}
