package database

import (
	"fmt"
	"log"
	"time"

	"github.com/pikez/zcli/config"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var DB *gorm.DB

// InitDB initializes the database connection using GORM
func InitDB(cfg config.MySQLConfig) error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.User, cfg.Password, cfg.Host, cfg.Port, cfg.Database)

	var err error
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// Get generic database object sql.DB to use its functions
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get generic database object: %w", err)
	}

	// Set connection pool settings
	sqlDB.SetMaxOpenConns(10) // Max open connections
	sqlDB.SetMaxIdleConns(5)  // Max idle connections
	sqlDB.SetConnMaxLifetime(5 * time.Minute) // Max connection lifetime

	log.Println("Successfully connected to MySQL database with GORM!")
	return nil
}
