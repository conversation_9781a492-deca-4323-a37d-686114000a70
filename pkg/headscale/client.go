package headscale

import (
	"fmt"
	"net/http"
	"time"

	"github.com/pikez/zcli/pkg/httpClient"
)

const (
	defaultBaseURL = "http://localhost:8080"
)

// Client represents the Headscale API client.
type Client struct {
	HTTPClient *httpClient.Client
}

// NewClient creates a new Headscale API client.
func NewClient(baseURL string, token string) *Client {
	if baseURL == "" {
		baseURL = defaultBaseURL
	}
	hc := httpClient.NewClient(baseURL, 10*time.Second)
	hc.SetHeader("Authorization", "Bearer "+token)
	return &Client{
		HTTPClient: hc,
	}
}

// v1Node represents a node in Headscale.
type v1Node struct {
	ID             string       `json:"id"`
	MachineKey     string       `json:"machineKey"`
	NodeKey        string       `json:"nodeKey"`
	DiscoKey       string       `json:"discoKey"`
	IPAddresses    []string     `json:"ipAddresses"`
	Name           string       `json:"name"`
	User           v1User       `json:"user"`
	LastSeen       time.Time    `json:"lastSeen"`
	Expiry         time.Time    `json:"expiry"`
	PreAuthKey     v1PreAuthKey `json:"preAuthKey"`
	CreatedAt      time.Time    `json:"createdAt"`
	RegisterMethod string       `json:"registerMethod"`
	ForcedTags     []string     `json:"forcedTags"`
	InvalidTags    []string     `json:"invalidTags"`
	ValidTags      []string     `json:"validTags"`
	GivenName      string       `json:"givenName"`
	Online         bool         `json:"online"`
}

// v1User represents a user in Headscale.
type v1User struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"createdAt"`
}

// v1PreAuthKey represents a pre-authentication key in Headscale.
type v1PreAuthKey struct {
	User       string    `json:"user"`
	ID         string    `json:"id"`
	Key        string    `json:"key"`
	Reusable   bool      `json:"reusable"`
	Ephemeral  bool      `json:"ephemeral"`
	Used       bool      `json:"used"`
	Expiration time.Time `json:"expiration"`
	CreatedAt  time.Time `json:"createdAt"`
	ACLTags    []string  `json:"aclTags"`
}

// v1ListNodesResponse represents the response for listing nodes.
type v1ListNodesResponse struct {
	Nodes []v1Node `json:"nodes"`
}

// ListNodes lists all nodes.
func (c *Client) ListNodes() (*v1ListNodesResponse, error) {
	var result v1ListNodesResponse
	if err := c.HTTPClient.Do(http.MethodGet, "/api/v1/node", nil, &result); err != nil {
		return nil, fmt.Errorf("failed to list nodes: %w", err)
	}
	return &result, nil
}

// v1GetUserResponse represents the response for getting a user.
type v1GetUserResponse struct {
	User v1User `json:"user"`
}

// GetUser gets a user by name.
func (c *Client) GetUser(name string) (*v1GetUserResponse, error) {
	path := fmt.Sprintf("/api/v1/user/%s", name)
	var result v1GetUserResponse
	if err := c.HTTPClient.Do(http.MethodGet, path, nil, &result); err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &result, nil
}

// v1CreateUserRequest represents the request for creating a user.
type v1CreateUserRequest struct {
	Name string `json:"name"`
}

// v1CreateUserResponse represents the response for creating a user.
type v1CreateUserResponse struct {
	User v1User `json:"user"`
}

// CreateUser creates a new user.
func (c *Client) CreateUser(name string) (*v1CreateUserResponse, error) {
	requestBody := v1CreateUserRequest{Name: name}
	var result v1CreateUserResponse
	if err := c.HTTPClient.Do(http.MethodPost, "/api/v1/user", requestBody, &result); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}
	return &result, nil
}

// v1DeleteUserResponse represents the response for deleting a user.
type v1DeleteUserResponse struct{}

// DeleteUser deletes a user by name.
func (c *Client) DeleteUser(name string) (*v1DeleteUserResponse, error) {
	path := fmt.Sprintf("/api/v1/user/%s", name)
	var result v1DeleteUserResponse
	if err := c.HTTPClient.Do(http.MethodDelete, path, nil, &result); err != nil {
		return nil, fmt.Errorf("failed to delete user: %w", err)
	}
	return &result, nil
}