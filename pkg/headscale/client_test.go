package headscale

import (
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestNewClient(t *testing.T) {
	// 创建一个模拟服务器来模拟Headscale API
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 对于这个测试，我们不需要服务器做任何特殊的事情。
		// 一个 200 OK 的响应就足以让客户端被创建。
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	// 测试用例1：成功创建客户端
	t.Run("successful client creation", func(t *testing.T) {
		token := "test-token"

		client := NewClient(server.URL, token)

		if client == nil {
			t.Error("NewClient() returned nil, expected a client instance")
		}
	})

	// 原始的 NewClient 函数在出错时会调用 logrus.Fatal，这将导致测试退出。
	// 为了正确地测试错误情况，应该重构 NewClient 函数，
	// 让它返回一个错误而不是调用 logrus.Fatal。
	// 目前，我们只测试成功路径。
}

func TestNewRealClient(t *testing.T) {
	token := "3m5uA-GY3g.LvKMHWiIX1UTKLlKFuyQFxGYUyyUyum8BGiLVSF2zIE"
	url := "https://headscale.pikez.fun"

	client := NewClient(url, token)

	if client == nil {
		t.Error("NewClient() returned nil, expected a client instance")
	}
	client.ListNodes()
}
