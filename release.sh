#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
MINIO_ALIAS="fn" # Replace with your MinIO alias
MINIO_BUCKET="homelab"
MINIO_PATH="zcli"
BINARY_NAME="zcli"

# --- Check for mc client ---
if ! command -v mc &> /dev/null
then
    echo "Error: MinIO client (mc) is not installed or not in PATH."
    echo "Please install mc and configure your MinIO alias."
    exit 1
fi

# --- Get build info ---
COMMIT=$(git rev-parse --short HEAD)
BRANCH_TAG=$(git describe --tags --exact-match 2>/dev/null || git rev-parse --abbrev-ref HEAD)
BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')

LDFLAGS="-X main.Version=${BRANCH_TAG} -X main.Commit=${COMMIT} -X main.BuildDate=${BUILD_DATE} -X main.BranchTag=${BR<PERSON>CH_TAG}"

# --- Compile for Linux ---
echo "Compiling for Linux (amd64)..."
GOOS=linux GOARCH=amd64 go build -ldflags "${LDFLAGS}" -o ${BINARY_NAME}-linux-amd64 main.go
echo "Linux binary compiled: ${BINARY_NAME}-linux-amd64"

# --- Compile for macOS ---
echo "Compiling for macOS (amd64)..."
GOOS=darwin GOARCH=amd64 go build -ldflags "${LDFLAGS}" -o ${BINARY_NAME}-darwin-amd64 main.go
echo "macOS binary compiled: ${BINARY_NAME}-darwin-amd64"

echo "Compiling for macOS (arm64)..."
GOOS=darwin GOARCH=arm64 go build -ldflags "${LDFLAGS}" -o ${BINARY_NAME}-darwin-arm64 main.go
echo "macOS binary compiled: ${BINARY_NAME}-darwin-arm64"

# --- Upload to MinIO ---
echo "Uploading binaries to MinIO bucket '${MINIO_BUCKET}' at path '${MINIO_PATH}'..."

# Upload Linux binary
mc cp ${BINARY_NAME}-linux-amd64 ${MINIO_ALIAS}/${MINIO_BUCKET}/${MINIO_PATH}/
echo "Uploaded ${BINARY_NAME}-linux-amd64"

# Upload macOS binary
mc cp ${BINARY_NAME}-darwin-amd64 ${MINIO_ALIAS}/${MINIO_BUCKET}/${MINIO_PATH}/
echo "Uploaded ${BINARY_NAME}-darwin-amd64"

mc cp ${BINARY_NAME}-darwin-arm64 ${MINIO_ALIAS}/${MINIO_BUCKET}/${MINIO_PATH}/
echo "Uploaded ${BINARY_NAME}-darwin-arm64"

# Upload .zcli.yaml
mc cp .zcli.yaml ${MINIO_ALIAS}/${MINIO_BUCKET}/${MINIO_PATH}/
echo "Uploaded .zcli.yaml"

# Upload install.sh
mc cp install.sh ${MINIO_ALIAS}/${MINIO_BUCKET}/${MINIO_PATH}/
echo "Uploaded install.sh"

echo "Release process completed successfully!"

# --- Clean up compiled binaries ---
echo "Cleaning up compiled binaries..."
rm ${BINARY_NAME}-linux-amd64 ${BINARY_NAME}-darwin-amd64 ${BINARY_NAME}-darwin-arm64
echo "Clean up complete."
