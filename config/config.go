package config

import (
	"github.com/go-playground/validator/v10"
	"github.com/spf13/viper"
)

// Config stores all configuration for the application.
// The values are read by viper from a config file or environment variables.
type Config struct {
	Author         string `mapstructure:"author"`
	HeadscaleURL   string `mapstructure:"headscale_url"`
	HeadscaleToken string `mapstructure:"headscale_token"`
	CloudflareKey  string `mapstructure:"cloudflare_key"`
	APIBaseURL     string `mapstructure:"api_base_url"`
	MySQL          MySQLConfig `mapstructure:"mysql"`
}

// MySQLConfig stores MySQL database configuration.
type MySQLConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`	
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"database"`
}

// LoadConfig reads configuration from file or environment variables.
func LoadConfig() (config Config, err error) {
	err = viper.Unmarshal(&config)
	if err != nil {
		return
	}

	validate := validator.New()
	err = validate.Struct(config)
	return
}
